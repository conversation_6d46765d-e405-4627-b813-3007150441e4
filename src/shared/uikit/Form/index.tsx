import type { RefObject } from 'react';
import React, { useRef } from 'react';
import { Formik, Form as FormikForm } from 'formik';
import isEmptyObjectValues from 'shared/utils/toolkit/isEmptyObjectValues';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import type { FormikConfig, FormikProps, FormikValues } from 'formik';
import { scrollToFormError } from 'shared/utils/toolkit/focusAndScroll';

export interface FormProps<
  V extends FormikValues = FormikValues,
  D = any,
  R = any,
> extends Omit<FormikConfig<V>, 'onSubmit'> {
  initialValues: V;
  onSuccess?: (
    apiResponse: any,
    values?: any,
    formikRef?: FormikProps<V>
  ) => void;
  onFailure?: (error: any, variables: any, formRef?: FormikProps<any>) => any;
  validationSchema?: {};
  url?: string;
  local?: boolean;
  method?: 'POST' | 'DELETE' | 'PUT';
  headers?: any;
  transform?: (values: V) => D;
  customErrorHandler?: Function;
  apiFunc?: (data: D) => Promise<R>;
  customXNames?: Array<string>;
  submitWithEnter?: boolean;
  className?: string;
  innerRef?: RefObject<FormikProps<V>>;
  scrollToError?: boolean; // Enable/disable automatic scroll to error functionality
  scrollToErrorOptions?: {
    behavior?: ScrollBehavior;
    block?: ScrollLogicalPosition;
    inline?: ScrollLogicalPosition;
    focus?: boolean;
    timeout?: number;
  };
}

const Form = <V extends FormikValues, D>({
  initialValues,
  validationSchema,
  children,
  onFailure,
  onSuccess: onSuccessHandler,
  enableReinitialize,
  url,
  local,
  method = 'POST',
  headers = {},
  transform,
  customErrorHandler,
  apiFunc,
  customXNames,
  submitWithEnter,
  className,
  innerRef,
  validate,
  scrollToError = true,
  scrollToErrorOptions = {},
}: FormProps<V, D>) => {
  const formRef = innerRef || useRef<FormikProps<V>>(null);

  const onSuccess = (response: any, variables: any) => {
    formRef?.current?.setSubmitting(false);

    return onSuccessHandler?.(
      response?.data || response,
      variables,
      formRef.current!
    );
  };

  const onErrorHandler = async (error: any, variables: any) => {
    await customErrorHandler?.({ error, variables, form: formRef?.current });
    formRef?.current?.setSubmitting(false);
    onFailure?.(error, variables, formRef.current!);
    const fieldErrors = error?.response?.data?.fieldErrors;

    if (fieldErrors?.length > 0) {
      const formErrors = fieldErrors?.reduce(
        (prev: {}, cur: { field: string; defaultMessage: string }) => {
          const field = customXNames?.includes(cur?.field)
            ? `x${cur?.field}`
            : cur?.field;
          const message = cur?.defaultMessage;
          return { ...prev, [field]: message };
        },
        {}
      );
      formRef?.current?.setStatus(formErrors);
      if (scrollToError) {
        setTimeout(() => {
          scrollToFormError(
            {
              errors: formRef?.current?.errors,
              status: formErrors,
            },
            scrollToErrorOptions
          );
        }, scrollToErrorOptions.timeout || 100);
      }
    }
  };

  const { mutate } = useReactMutation<D>({ apiFunc, url, headers, method });
  const onSubmitHandler = (values: any, actions: any) => {
    actions.setStatus(undefined);
    const bodyData = transform ? transform(values) : values;
    if (local) {
      return onSuccessHandler?.(bodyData, formRef.current);
    }
    actions.setSubmitting(true);
    mutate(bodyData, { onError: onErrorHandler, onSuccess });
  };

  const handleSubmit = async () => {
    const { isSubmitting, dirty, isValid, status, errors } =
      formRef.current || {};
    if (!isSubmitting && dirty && isValid && isEmptyObjectValues(status)) {
      formRef.current?.handleSubmit();
    } else if (!isValid && (errors || status) && scrollToError) {
      setTimeout(() => {
        scrollToFormError(
          {
            errors: formRef?.current?.errors,
            status: formRef?.current?.status,
          },
          scrollToErrorOptions
        );
      }, scrollToErrorOptions.timeout || 100);
    }
  };

  return (
    <Formik
      enableReinitialize={enableReinitialize}
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={onSubmitHandler}
      innerRef={formRef}
      // validateOnBlur={false}
      // validateOnChange={false}
      validateOnMount
      validate={validate}
    >
      {(props) => (
        <FormikForm
          className={className}
          onSubmit={
            submitWithEnter
              ? (e) => {
                  e.preventDefault();
                  handleSubmit();
                }
              : undefined
          }
          onKeyDown={
            submitWithEnter
              ? (e) => {
                  if (e.key === 'Enter') {
                    handleSubmit();
                  }
                }
              : undefined
          }
        >
          {typeof children === 'function' ? children(props) : children}
        </FormikForm>
      )}
    </Formik>
  );
};

export default Form;
